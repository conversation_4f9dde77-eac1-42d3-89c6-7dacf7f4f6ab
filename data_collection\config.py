# Configuration file for API keys and settings

# API Keys (loaded from env keys file)
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
GOOGLE_PLACES_API_KEY = "AIzaSyC-aLrX3HmDxCjR3STYJ3Y4TUR4z12I_mI"

# Pittsburgh coordinates and search radius
PITTSBURGH_LAT = 40.4406
PITTSBURGH_LNG = -79.9959
SEARCH_RADIUS_METERS = 160934  # 100 miles in meters

# Business categories for coffee subscription targeting
BUSINESS_CATEGORIES = [
    # Professional services
    "accounting", "lawyer", "insurance_agency", "real_estate_agency",
    "local_government_office", "consulting", "marketing", "architecture",
    "engineering", "financial_services",
    
    # Healthcare
    "doctor", "dentist", "hospital", "clinic", "pharmacy", "veterinary_care",
    
    # Financial
    "bank", "credit_union", "investment",
    
    # Other office-based businesses
    "office", "corporate_office", "business_center", "coworking_space"
]

# Yellow Pages search terms
YELLOWPAGES_SEARCH_TERMS = [
    "offices", "law firms", "medical offices", "accounting firms",
    "insurance agencies", "real estate offices", "consulting firms",
    "marketing agencies", "architecture firms", "engineering firms",
    "financial services", "banks", "credit unions", "business centers"
]

# Cities within 100 miles of Pittsburgh for Yellow Pages searches
PITTSBURGH_AREA_CITIES = [
    "Pittsburgh PA", "Cranberry Township PA", "Bethel Park PA", 
    "Mt Lebanon PA", "Upper St Clair PA", "Peters Township PA",
    "Moon Township PA", "Robinson Township PA", "Wexford PA",
    "Sewickley PA", "Fox Chapel PA", "Murrysville PA",
    "Monroeville PA", "Greensburg PA", "Washington PA",
    "Butler PA", "Beaver PA", "New Castle PA", "Uniontown PA"
]

# Overpass API amenity types
OVERPASS_AMENITIES = [
    "office", "bank", "clinic", "hospital", "pharmacy", 
    "restaurant", "cafe", "bar", "pub"
]

# Google Places API types
GOOGLE_PLACES_TYPES = [
    "accounting", "bank", "cafe", "doctor", "dentist", "hospital",
    "insurance_agency", "lawyer", "local_government_office", "pharmacy",
    "real_estate_agency", "restaurant", "veterinary_care", "establishment"
]

# Data quality settings
MIN_BUSINESS_NAME_LENGTH = 3
MAX_BUSINESS_NAME_LENGTH = 100
FUZZY_MATCH_THRESHOLD = 0.9

# Rate limiting settings
GOOGLE_PLACES_DELAY = 0.1  # seconds between requests
YELLOWPAGES_DELAY = 1.0    # seconds between requests
OVERPASS_DELAY = 0.5       # seconds between requests

# Output file paths
OUTPUT_DIR = "output"
OVERPASS_OUTPUT = f"{OUTPUT_DIR}/overpass_data.csv"
GOOGLE_PLACES_OUTPUT = f"{OUTPUT_DIR}/google_places_data.csv"
YELLOWPAGES_OUTPUT = f"{OUTPUT_DIR}/yellowpages_data.csv"
NORMALIZED_OUTPUT = f"{OUTPUT_DIR}/normalized_master_data.csv"
