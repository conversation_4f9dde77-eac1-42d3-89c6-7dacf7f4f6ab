#!/usr/bin/env python3
"""
Data normalizer to combine and deduplicate business data from multiple sources
"""

import pandas as pd
import numpy as np
import logging
from typing import List, Dict, Any, Optional
from fuzzywuzzy import fuzz
import phonenumbers
import validators
from datetime import datetime
import re

from config import (
    OVERPASS_OUTPUT, GOOGLE_PLACES_OUTPUT, YELLOWPAGES_OUTPUT, NORMALIZED_OUTPUT,
    MIN_BUSINESS_NAME_LENGTH, MAX_BUSINESS_NAME_LENGTH, FUZZY_MATCH_THRESHOLD
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataNormalizer:
    def __init__(self):
        self.normalized_data = []
        self.duplicate_groups = []
        
    def load_data_sources(self) -> Dict[str, pd.DataFrame]:
        """Load data from all CSV sources"""
        data_sources = {}
        
        # Load Overpass data
        try:
            overpass_df = pd.read_csv(OVERPASS_OUTPUT)
            data_sources['overpass'] = overpass_df
            logger.info(f"Loaded {len(overpass_df)} records from Overpass API")
        except FileNotFoundError:
            logger.warning("Overpass data file not found")
            data_sources['overpass'] = pd.DataFrame()
        
        # Load Google Places data
        try:
            google_df = pd.read_csv(GOOGLE_PLACES_OUTPUT)
            data_sources['google_places'] = google_df
            logger.info(f"Loaded {len(google_df)} records from Google Places API")
        except FileNotFoundError:
            logger.warning("Google Places data file not found")
            data_sources['google_places'] = pd.DataFrame()
        
        # Load Yellow Pages data
        try:
            yellowpages_df = pd.read_csv(YELLOWPAGES_OUTPUT)
            data_sources['yellowpages'] = yellowpages_df
            logger.info(f"Loaded {len(yellowpages_df)} records from Yellow Pages")
        except FileNotFoundError:
            logger.warning("Yellow Pages data file not found")
            data_sources['yellowpages'] = pd.DataFrame()
        
        return data_sources
    
    def normalize_phone_number(self, phone: str) -> Optional[str]:
        """Normalize phone number to standard format"""
        if not phone or pd.isna(phone):
            return None
        
        try:
            # Clean the phone number
            phone_str = str(phone).strip()
            
            # Try to parse as US number
            parsed = phonenumbers.parse(phone_str, "US")
            if phonenumbers.is_valid_number(parsed):
                return phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.E164)
        except:
            pass
        
        # Fallback: extract digits and format if 10 digits
        digits = re.sub(r'\D', '', phone_str)
        if len(digits) == 10:
            return f"+1{digits}"
        elif len(digits) == 11 and digits.startswith('1'):
            return f"+{digits}"
        
        return phone_str  # Return original if can't normalize
    
    def normalize_website(self, website: str) -> Optional[str]:
        """Normalize website URL"""
        if not website or pd.isna(website):
            return None
        
        website_str = str(website).strip().lower()
        
        # Add protocol if missing
        if not website_str.startswith(('http://', 'https://')):
            website_str = 'https://' + website_str
        
        # Validate URL
        if validators.url(website_str):
            return website_str
        
        return None
    
    def clean_business_name(self, name: str) -> Optional[str]:
        """Clean and validate business name"""
        if not name or pd.isna(name):
            return None
        
        name_str = str(name).strip()
        
        # Remove extra whitespace
        name_str = re.sub(r'\s+', ' ', name_str)
        
        # Check length constraints
        if len(name_str) < MIN_BUSINESS_NAME_LENGTH or len(name_str) > MAX_BUSINESS_NAME_LENGTH:
            return None
        
        # Filter out obviously invalid names
        invalid_patterns = [
            r'^unknown\s*business$',
            r'^business$',
            r'^office$',
            r'^building$',
            r'^\d+$',  # Just numbers
        ]
        
        for pattern in invalid_patterns:
            if re.match(pattern, name_str, re.IGNORECASE):
                return None
        
        return name_str
    
    def normalize_address(self, address: str, street: str, city: str, state: str, zipcode: str) -> Dict[str, str]:
        """Normalize address components"""
        result = {
            'address': None,
            'street': None,
            'city': None,
            'state': None,
            'zipcode': None
        }
        
        # Clean individual components
        if street and not pd.isna(street):
            result['street'] = str(street).strip()
        
        if city and not pd.isna(city):
            result['city'] = str(city).strip().title()
        
        if state and not pd.isna(state):
            state_str = str(state).strip().upper()
            # Normalize to 2-letter state code
            if len(state_str) > 2:
                state_mapping = {
                    'PENNSYLVANIA': 'PA', 'PENN': 'PA',
                    'OHIO': 'OH', 'WEST VIRGINIA': 'WV',
                    'MARYLAND': 'MD', 'NEW YORK': 'NY'
                }
                result['state'] = state_mapping.get(state_str, state_str[:2])
            else:
                result['state'] = state_str
        
        if zipcode and not pd.isna(zipcode):
            zip_str = str(zipcode).strip()
            # Extract 5-digit ZIP code
            zip_match = re.search(r'\d{5}', zip_str)
            if zip_match:
                result['zipcode'] = zip_match.group()
        
        # Build full address if not provided
        if address and not pd.isna(address):
            result['address'] = str(address).strip()
        else:
            address_parts = [result['street'], result['city'], result['state'], result['zipcode']]
            full_address = ', '.join([part for part in address_parts if part])
            result['address'] = full_address if full_address else None
        
        return result
    
    def normalize_record(self, record: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Normalize a single business record"""
        
        # Clean business name
        clean_name = self.clean_business_name(record.get('business_name'))
        if not clean_name:
            return None
        
        # Normalize address
        address_info = self.normalize_address(
            record.get('address'),
            record.get('street'),
            record.get('city'),
            record.get('state'),
            record.get('zipcode')
        )
        
        # Normalize phone and website
        clean_phone = self.normalize_phone_number(record.get('phone'))
        clean_website = self.normalize_website(record.get('website'))
        
        # Build normalized record
        normalized = {
            'business_name': clean_name,
            'address': address_info['address'],
            'street': address_info['street'],
            'city': address_info['city'],
            'state': address_info['state'],
            'zipcode': address_info['zipcode'],
            'phone': clean_phone,
            'website': clean_website,
            'email': record.get('email'),
            'category': record.get('category'),
            'latitude': record.get('latitude'),
            'longitude': record.get('longitude'),
            'source': record.get('source'),
            'source_id': record.get('source_id'),
            'rating': record.get('rating'),
            'scraped_date': datetime.now().isoformat()
        }
        
        return normalized
    
    def find_duplicates(self, records: List[Dict[str, Any]]) -> List[List[int]]:
        """Find duplicate records using fuzzy matching"""
        logger.info("Finding duplicates...")
        
        duplicate_groups = []
        processed = set()
        
        for i, record1 in enumerate(records):
            if i in processed:
                continue
            
            group = [i]
            
            for j, record2 in enumerate(records[i+1:], i+1):
                if j in processed:
                    continue
                
                # Check for duplicates
                is_duplicate = False
                
                # Exact name and address match
                if (record1['business_name'] == record2['business_name'] and
                    record1['address'] == record2['address']):
                    is_duplicate = True
                
                # Fuzzy name match with same ZIP code
                elif (record1['zipcode'] and record2['zipcode'] and
                      record1['zipcode'] == record2['zipcode']):
                    name_similarity = fuzz.ratio(record1['business_name'], record2['business_name'])
                    if name_similarity >= FUZZY_MATCH_THRESHOLD * 100:
                        is_duplicate = True
                
                # Phone number match
                elif (record1['phone'] and record2['phone'] and
                      record1['phone'] == record2['phone']):
                    is_duplicate = True
                
                # Website match
                elif (record1['website'] and record2['website'] and
                      record1['website'] == record2['website']):
                    is_duplicate = True
                
                if is_duplicate:
                    group.append(j)
            
            if len(group) > 1:
                duplicate_groups.append(group)
                processed.update(group)
            else:
                processed.add(i)
        
        logger.info(f"Found {len(duplicate_groups)} duplicate groups")
        return duplicate_groups
    
    def merge_duplicates(self, records: List[Dict[str, Any]], duplicate_groups: List[List[int]]) -> List[Dict[str, Any]]:
        """Merge duplicate records, keeping the best information"""
        logger.info("Merging duplicates...")
        
        merged_records = []
        processed = set()
        
        # Process duplicate groups
        for group in duplicate_groups:
            # Find the best record in the group (prefer Google Places, then Overpass, then Yellow Pages)
            source_priority = {'google_places': 3, 'overpass': 2, 'yellowpages': 1}
            
            best_record = None
            best_priority = 0
            
            for idx in group:
                record = records[idx]
                priority = source_priority.get(record['source'], 0)
                
                # Also consider data completeness
                completeness = sum([
                    1 if record.get('phone') else 0,
                    1 if record.get('website') else 0,
                    1 if record.get('email') else 0,
                    1 if record.get('address') else 0,
                    1 if record.get('latitude') else 0
                ])
                
                total_score = priority + completeness * 0.1
                
                if total_score > best_priority:
                    best_priority = total_score
                    best_record = record.copy()
            
            # Merge information from other records in the group
            for idx in group:
                record = records[idx]
                
                # Fill in missing information
                for field in ['phone', 'website', 'email', 'address', 'latitude', 'longitude']:
                    if not best_record.get(field) and record.get(field):
                        best_record[field] = record[field]
                
                # Combine sources
                if best_record['source'] != record['source']:
                    best_record['source'] = f"{best_record['source']}, {record['source']}"
            
            merged_records.append(best_record)
            processed.update(group)
        
        # Add non-duplicate records
        for i, record in enumerate(records):
            if i not in processed:
                merged_records.append(record)
        
        logger.info(f"Merged to {len(merged_records)} unique records")
        return merged_records
    
    def normalize_all_data(self) -> pd.DataFrame:
        """Main function to normalize all data"""
        logger.info("Starting data normalization...")
        
        # Load all data sources
        data_sources = self.load_data_sources()
        
        # Combine all records
        all_records = []
        for source_name, df in data_sources.items():
            if not df.empty:
                for _, row in df.iterrows():
                    record = row.to_dict()
                    normalized = self.normalize_record(record)
                    if normalized:
                        all_records.append(normalized)
        
        logger.info(f"Normalized {len(all_records)} total records")
        
        if not all_records:
            logger.warning("No valid records found!")
            return pd.DataFrame()
        
        # Find and merge duplicates
        duplicate_groups = self.find_duplicates(all_records)
        final_records = self.merge_duplicates(all_records, duplicate_groups)
        
        # Convert to DataFrame
        df = pd.DataFrame(final_records)
        
        # Final cleanup and sorting
        df = df.sort_values(['business_name', 'city'])
        df = df.reset_index(drop=True)
        
        logger.info(f"Final dataset: {len(df)} unique businesses")
        return df
    
    def save_normalized_data(self, df: pd.DataFrame, filename: str):
        """Save normalized data to CSV"""
        if df.empty:
            logger.warning("No data to save")
            return
        
        df.to_csv(filename, index=False)
        logger.info(f"Saved {len(df)} normalized records to {filename}")

def main():
    """Main function to run data normalization"""
    normalizer = DataNormalizer()
    
    # Normalize all data
    normalized_df = normalizer.normalize_all_data()
    
    # Save normalized data
    normalizer.save_normalized_data(normalized_df, NORMALIZED_OUTPUT)
    
    # Print summary
    if not normalized_df.empty:
        print(f"\n=== Data Normalization Complete ===")
        print(f"Total unique businesses: {len(normalized_df)}")
        print(f"Data saved to: {NORMALIZED_OUTPUT}")
        
        # Show data quality metrics
        print(f"\nData Quality Metrics:")
        print(f"Records with phone: {normalized_df['phone'].notna().sum()}")
        print(f"Records with website: {normalized_df['website'].notna().sum()}")
        print(f"Records with email: {normalized_df['email'].notna().sum()}")
        print(f"Records with coordinates: {normalized_df['latitude'].notna().sum()}")
        
        # Show sample data
        print(f"\nSample data:")
        print(normalized_df[['business_name', 'category', 'city', 'phone', 'website']].head())
        
        # Show source breakdown
        print(f"\nSource breakdown:")
        print(normalized_df['source'].value_counts())
        
        # Show category breakdown
        print(f"\nTop categories:")
        print(normalized_df['category'].value_counts().head(10))
    else:
        print("No data to normalize!")

if __name__ == "__main__":
    main()
