#!/usr/bin/env python3
"""
Main orchestrator script to run all business data scrapers
Coordinates Overpass API, Google Places API, and Yellow Pages scraping
"""

import logging
import time
import sys
import os
from datetime import datetime
import argparse

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from overpass_scraper import OverpassScraper
from google_places_scraper import GooglePlacesScraper
from yellowpages_scraper import YellowPagesScraper
from data_normalizer import DataNormalizer

from config import (
    OVERPASS_OUTPUT, GOOGLE_PLACES_OUTPUT, YELLOWPAGES_OUTPUT, NORMALIZED_OUTPUT
)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraping.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BusinessDataOrchestrator:
    def __init__(self):
        self.start_time = datetime.now()
        self.results = {
            'overpass': 0,
            'google_places': 0,
            'yellowpages': 0,
            'normalized': 0
        }
    
    def run_overpass_scraper(self) -> bool:
        """Run Overpass API scraper"""
        logger.info("=" * 60)
        logger.info("STARTING OVERPASS API SCRAPING")
        logger.info("=" * 60)
        
        try:
            scraper = OverpassScraper()
            businesses = scraper.scrape_businesses()
            scraper.save_to_csv(businesses, OVERPASS_OUTPUT)
            
            self.results['overpass'] = len(businesses)
            logger.info(f"Overpass scraping completed: {len(businesses)} businesses")
            return True
            
        except Exception as e:
            logger.error(f"Overpass scraping failed: {e}")
            return False
    
    def run_google_places_scraper(self) -> bool:
        """Run Google Places API scraper"""
        logger.info("=" * 60)
        logger.info("STARTING GOOGLE PLACES API SCRAPING")
        logger.info("=" * 60)
        
        try:
            scraper = GooglePlacesScraper()
            businesses = scraper.scrape_businesses()
            scraper.save_to_csv(businesses, GOOGLE_PLACES_OUTPUT)
            
            self.results['google_places'] = len(businesses)
            logger.info(f"Google Places scraping completed: {len(businesses)} businesses")
            return True
            
        except Exception as e:
            logger.error(f"Google Places scraping failed: {e}")
            return False
    
    def run_yellowpages_scraper(self) -> bool:
        """Run Yellow Pages scraper"""
        logger.info("=" * 60)
        logger.info("STARTING YELLOW PAGES SCRAPING")
        logger.info("=" * 60)
        
        try:
            scraper = YellowPagesScraper()
            businesses = scraper.scrape_all_combinations()
            scraper.save_to_csv(businesses, YELLOWPAGES_OUTPUT)
            
            self.results['yellowpages'] = len(businesses)
            logger.info(f"Yellow Pages scraping completed: {len(businesses)} businesses")
            return True
            
        except Exception as e:
            logger.error(f"Yellow Pages scraping failed: {e}")
            return False
    
    def run_data_normalizer(self) -> bool:
        """Run data normalizer"""
        logger.info("=" * 60)
        logger.info("STARTING DATA NORMALIZATION")
        logger.info("=" * 60)
        
        try:
            normalizer = DataNormalizer()
            normalized_df = normalizer.normalize_all_data()
            normalizer.save_normalized_data(normalized_df, NORMALIZED_OUTPUT)
            
            self.results['normalized'] = len(normalized_df)
            logger.info(f"Data normalization completed: {len(normalized_df)} unique businesses")
            return True
            
        except Exception as e:
            logger.error(f"Data normalization failed: {e}")
            return False
    
    def print_final_summary(self):
        """Print final summary of all scraping results"""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        print("\n" + "=" * 80)
        print("BUSINESS DATA COLLECTION COMPLETE")
        print("=" * 80)
        print(f"Start time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"End time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Total duration: {duration}")
        print()
        
        print("RESULTS SUMMARY:")
        print("-" * 40)
        print(f"Overpass API:     {self.results['overpass']:,} businesses")
        print(f"Google Places:    {self.results['google_places']:,} businesses")
        print(f"Yellow Pages:     {self.results['yellowpages']:,} businesses")
        print(f"Total Raw:        {sum([self.results['overpass'], self.results['google_places'], self.results['yellowpages']]):,} businesses")
        print(f"Unique Final:     {self.results['normalized']:,} businesses")
        print()
        
        print("OUTPUT FILES:")
        print("-" * 40)
        print(f"Overpass data:    {OVERPASS_OUTPUT}")
        print(f"Google Places:    {GOOGLE_PLACES_OUTPUT}")
        print(f"Yellow Pages:     {YELLOWPAGES_OUTPUT}")
        print(f"Normalized data:  {NORMALIZED_OUTPUT}")
        print()
        
        if self.results['normalized'] > 0:
            print("SUCCESS: Data collection completed successfully!")
            print(f"You now have {self.results['normalized']:,} unique businesses for your coffee subscription lead generation.")
        else:
            print("WARNING: No final data was generated. Check the logs for errors.")
        
        print("=" * 80)
    
    def run_all_scrapers(self, skip_overpass=False, skip_google=False, skip_yellowpages=False):
        """Run all scrapers in sequence"""
        logger.info("Starting comprehensive business data collection...")
        logger.info(f"Target: Businesses within 100 miles of Pittsburgh, PA")
        logger.info(f"Focus: Coffee subscription prospects (offices, professional services)")
        
        success_count = 0
        total_scrapers = 3
        
        # Run Overpass scraper
        if not skip_overpass:
            if self.run_overpass_scraper():
                success_count += 1
            time.sleep(5)  # Brief pause between scrapers
        else:
            logger.info("Skipping Overpass API scraper")
            total_scrapers -= 1
        
        # Run Google Places scraper
        if not skip_google:
            if self.run_google_places_scraper():
                success_count += 1
            time.sleep(5)
        else:
            logger.info("Skipping Google Places API scraper")
            total_scrapers -= 1
        
        # Run Yellow Pages scraper
        if not skip_yellowpages:
            if self.run_yellowpages_scraper():
                success_count += 1
            time.sleep(5)
        else:
            logger.info("Skipping Yellow Pages scraper")
            total_scrapers -= 1
        
        # Run data normalizer if we have any data
        if success_count > 0:
            self.run_data_normalizer()
        else:
            logger.error("No scrapers succeeded - skipping data normalization")
        
        # Print final summary
        self.print_final_summary()
        
        return success_count == total_scrapers

def main():
    """Main function with command line argument support"""
    parser = argparse.ArgumentParser(description='Business Data Collection Orchestrator')
    parser.add_argument('--skip-overpass', action='store_true', help='Skip Overpass API scraping')
    parser.add_argument('--skip-google', action='store_true', help='Skip Google Places API scraping')
    parser.add_argument('--skip-yellowpages', action='store_true', help='Skip Yellow Pages scraping')
    parser.add_argument('--normalize-only', action='store_true', help='Only run data normalization')
    
    args = parser.parse_args()
    
    orchestrator = BusinessDataOrchestrator()
    
    if args.normalize_only:
        logger.info("Running data normalization only...")
        orchestrator.run_data_normalizer()
        orchestrator.print_final_summary()
    else:
        success = orchestrator.run_all_scrapers(
            skip_overpass=args.skip_overpass,
            skip_google=args.skip_google,
            skip_yellowpages=args.skip_yellowpages
        )
        
        if success:
            logger.info("All scrapers completed successfully!")
            sys.exit(0)
        else:
            logger.error("Some scrapers failed. Check the logs for details.")
            sys.exit(1)

if __name__ == "__main__":
    main()
