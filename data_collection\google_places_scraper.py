#!/usr/bin/env python3
"""
Google Places API scraper for collecting business data
Focuses on businesses within 100 miles of Pittsburgh, PA
"""

import googlemaps
import pandas as pd
import time
import logging
from typing import List, Dict, Any, Optional
from tqdm import tqdm
import json

from config import (
    GOOGLE_PLACES_API_KEY, PITTSBURGH_LAT, PITTSBURGH_LNG, SEARCH_RADIUS_METERS,
    GOOGLE_PLACES_TYPES, GOOGLE_PLACES_DELAY, GOOGLE_PLACES_OUTPUT,
    YELLOWPAGES_SEARCH_TERMS
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GooglePlacesScraper:
    def __init__(self):
        self.gmaps = googlemaps.Client(key=GOOGLE_PLACES_API_KEY)
        self.businesses = []
        self.processed_place_ids = set()
        
    def search_nearby_places(self, place_type: str) -> List[Dict[str, Any]]:
        """Search for nearby places of a specific type"""
        places = []
        next_page_token = None
        
        try:
            while True:
                logger.info(f"Searching for {place_type} places...")
                
                # Make API request
                if next_page_token:
                    time.sleep(2)  # Required delay for next_page_token
                    result = self.gmaps.places_nearby(
                        location=(PITTSBURGH_LAT, PITTSBURGH_LNG),
                        radius=min(SEARCH_RADIUS_METERS, 50000),  # Google Places max radius is 50km
                        type=place_type,
                        page_token=next_page_token
                    )
                else:
                    result = self.gmaps.places_nearby(
                        location=(PITTSBURGH_LAT, PITTSBURGH_LNG),
                        radius=min(SEARCH_RADIUS_METERS, 50000),
                        type=place_type
                    )
                
                if result.get('results'):
                    places.extend(result['results'])
                    logger.info(f"Found {len(result['results'])} places for {place_type}")
                
                # Check for next page
                next_page_token = result.get('next_page_token')
                if not next_page_token:
                    break
                    
                time.sleep(GOOGLE_PLACES_DELAY)
                
        except Exception as e:
            logger.error(f"Error searching for {place_type}: {e}")
        
        return places
    
    def search_text_places(self, query: str) -> List[Dict[str, Any]]:
        """Search for places using text search"""
        places = []
        next_page_token = None
        
        try:
            while True:
                logger.info(f"Text searching for: {query}")
                
                # Make API request
                if next_page_token:
                    time.sleep(2)
                    result = self.gmaps.places(
                        query=f"{query} Pittsburgh PA",
                        location=(PITTSBURGH_LAT, PITTSBURGH_LNG),
                        radius=min(SEARCH_RADIUS_METERS, 50000),
                        page_token=next_page_token
                    )
                else:
                    result = self.gmaps.places(
                        query=f"{query} Pittsburgh PA",
                        location=(PITTSBURGH_LAT, PITTSBURGH_LNG),
                        radius=min(SEARCH_RADIUS_METERS, 50000)
                    )
                
                if result.get('results'):
                    places.extend(result['results'])
                    logger.info(f"Found {len(result['results'])} places for '{query}'")
                
                # Check for next page
                next_page_token = result.get('next_page_token')
                if not next_page_token:
                    break
                    
                time.sleep(GOOGLE_PLACES_DELAY)
                
        except Exception as e:
            logger.error(f"Error text searching for '{query}': {e}")
        
        return places
    
    def get_place_details(self, place_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information for a specific place"""
        try:
            fields = [
                'name', 'formatted_address', 'formatted_phone_number', 
                'website', 'business_status', 'types', 'rating',
                'geometry', 'address_components', 'place_id'
            ]
            
            result = self.gmaps.place(place_id=place_id, fields=fields)
            return result.get('result')
            
        except Exception as e:
            logger.warning(f"Error getting details for place {place_id}: {e}")
            return None
    
    def extract_business_info(self, place: Dict[str, Any], detailed: bool = False) -> Dict[str, Any]:
        """Extract business information from Google Places result"""
        
        # Get coordinates
        geometry = place.get('geometry', {})
        location = geometry.get('location', {})
        lat = location.get('lat')
        lng = location.get('lng')
        
        # Extract address components
        address_components = place.get('address_components', [])
        street_number = ''
        route = ''
        city = ''
        state = ''
        zipcode = ''
        
        for component in address_components:
            types = component.get('types', [])
            if 'street_number' in types:
                street_number = component.get('long_name', '')
            elif 'route' in types:
                route = component.get('long_name', '')
            elif 'locality' in types:
                city = component.get('long_name', '')
            elif 'administrative_area_level_1' in types:
                state = component.get('short_name', '')
            elif 'postal_code' in types:
                zipcode = component.get('long_name', '')
        
        street = f"{street_number} {route}".strip()
        
        # Business types/categories
        types = place.get('types', [])
        category = ', '.join([t.replace('_', ' ').title() for t in types[:3]])
        
        business_info = {
            'business_name': place.get('name'),
            'address': place.get('formatted_address'),
            'street': street if street else None,
            'city': city if city else None,
            'state': state if state else None,
            'zipcode': zipcode if zipcode else None,
            'phone': place.get('formatted_phone_number'),
            'website': place.get('website'),
            'email': None,  # Google Places doesn't provide email
            'category': category,
            'latitude': lat,
            'longitude': lng,
            'source': 'google_places',
            'source_id': place.get('place_id'),
            'rating': place.get('rating'),
            'business_status': place.get('business_status'),
            'types': ', '.join(types)
        }
        
        return business_info
    
    def scrape_businesses(self) -> List[Dict[str, Any]]:
        """Scrape businesses using Google Places API"""
        logger.info("Starting Google Places API scraping...")
        
        all_places = []
        
        # Search by place types
        for place_type in tqdm(GOOGLE_PLACES_TYPES, desc="Searching by place types"):
            places = self.search_nearby_places(place_type)
            all_places.extend(places)
            time.sleep(GOOGLE_PLACES_DELAY)
        
        # Search by text queries
        for query in tqdm(YELLOWPAGES_SEARCH_TERMS, desc="Searching by text queries"):
            places = self.search_text_places(query)
            all_places.extend(places)
            time.sleep(GOOGLE_PLACES_DELAY)
        
        logger.info(f"Found {len(all_places)} total places (with duplicates)")
        
        # Remove duplicates and get detailed info
        businesses = []
        unique_place_ids = set()
        
        for place in tqdm(all_places, desc="Processing places"):
            place_id = place.get('place_id')
            if place_id and place_id not in unique_place_ids:
                unique_place_ids.add(place_id)
                
                # Get detailed information
                detailed_place = self.get_place_details(place_id)
                if detailed_place:
                    business_info = self.extract_business_info(detailed_place, detailed=True)
                else:
                    business_info = self.extract_business_info(place)
                
                # Filter out places without names or that are permanently closed
                if (business_info['business_name'] and 
                    business_info.get('business_status') != 'CLOSED_PERMANENTLY'):
                    businesses.append(business_info)
                
                time.sleep(GOOGLE_PLACES_DELAY)
        
        logger.info(f"Extracted {len(businesses)} unique businesses")
        return businesses
    
    def save_to_csv(self, businesses: List[Dict[str, Any]], filename: str):
        """Save businesses to CSV file"""
        if not businesses:
            logger.warning("No businesses to save")
            return
        
        df = pd.DataFrame(businesses)
        df.to_csv(filename, index=False)
        logger.info(f"Saved {len(businesses)} businesses to {filename}")

def main():
    """Main function to run Google Places scraper"""
    scraper = GooglePlacesScraper()
    
    # Scrape businesses
    businesses = scraper.scrape_businesses()
    
    # Save to CSV
    scraper.save_to_csv(businesses, GOOGLE_PLACES_OUTPUT)
    
    # Print summary
    if businesses:
        print(f"\n=== Google Places API Scraping Complete ===")
        print(f"Total businesses found: {len(businesses)}")
        print(f"Data saved to: {GOOGLE_PLACES_OUTPUT}")
        
        # Show sample data
        df = pd.DataFrame(businesses)
        print(f"\nSample data:")
        print(df[['business_name', 'category', 'city', 'phone', 'website']].head())
        
        # Show category breakdown
        print(f"\nCategory breakdown:")
        print(df['category'].value_counts().head(10))
    else:
        print("No businesses found!")

if __name__ == "__main__":
    main()
