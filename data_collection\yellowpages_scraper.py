#!/usr/bin/env python3
"""
Enhanced Yellow Pages scraper for Pittsburgh area businesses
Based on the existing yellow_pages.py but enhanced for lead generation
"""

import requests
from lxml import html
import pandas as pd
import time
import logging
from typing import List, Dict, Any
from tqdm import tqdm
import random
from urllib.parse import urljoin, urlparse

from config import (
    YELLOWPAGES_SEARCH_TERMS, PITTSBURGH_AREA_CITIES,
    YELLOWPAGES_DELAY, YELLOWPAGES_OUTPUT
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class YellowPagesScraper:
    def __init__(self):
        self.session = requests.Session()
        self.businesses = []
        
        # Headers to avoid being blocked
        self.headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Host': 'www.yellowpages.com',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        self.session.headers.update(self.headers)
    
    def parse_listing_page(self, keyword: str, place: str, page: int = 1) -> List[Dict[str, Any]]:
        """Parse a Yellow Pages listing page"""
        url = f"https://www.yellowpages.com/search?search_terms={keyword}&geo_location_terms={place}&page={page}"
        
        logger.info(f"Scraping: {url}")
        
        try:
            # Add random delay to avoid being blocked
            time.sleep(random.uniform(YELLOWPAGES_DELAY, YELLOWPAGES_DELAY * 2))
            
            response = self.session.get(url, verify=False, timeout=30)
            
            if response.status_code == 200:
                return self.extract_businesses_from_page(response.text, url)
            elif response.status_code == 404:
                logger.warning(f"No results found for {keyword} in {place}")
                return []
            else:
                logger.warning(f"HTTP {response.status_code} for {url}")
                return []
                
        except Exception as e:
            logger.error(f"Error scraping {url}: {e}")
            return []
    
    def extract_businesses_from_page(self, html_content: str, source_url: str) -> List[Dict[str, Any]]:
        """Extract business information from HTML content"""
        try:
            parser = html.fromstring(html_content)
            base_url = "https://www.yellowpages.com"
            parser.make_links_absolute(base_url)
            
            # XPath selectors for business listings
            XPATH_LISTINGS = "//div[@class='search-results organic']//div[@class='v-card']"
            listings = parser.xpath(XPATH_LISTINGS)
            
            businesses = []
            
            for listing in listings:
                try:
                    business_info = self.extract_business_from_listing(listing, source_url)
                    if business_info and business_info['business_name']:
                        businesses.append(business_info)
                except Exception as e:
                    logger.warning(f"Error extracting business from listing: {e}")
                    continue
            
            return businesses
            
        except Exception as e:
            logger.error(f"Error parsing HTML content: {e}")
            return []
    
    def extract_business_from_listing(self, listing, source_url: str) -> Dict[str, Any]:
        """Extract business information from a single listing"""
        
        # XPath selectors
        XPATH_BUSINESS_NAME = ".//a[@class='business-name']//text()"
        XPATH_BUSINESS_PAGE = ".//a[@class='business-name']//@href"
        XPATH_TELEPHONE = ".//div[@class='phones phone primary']//text()"
        XPATH_STREET = ".//div[@class='street-address']//text()"
        XPATH_LOCALITY = ".//div[@class='locality']//text()"
        XPATH_REGION = ".//div[@class='info']//div//p[@itemprop='address']//span[@itemprop='addressRegion']//text()"
        XPATH_ZIP_CODE = ".//div[@class='info']//div//p[@itemprop='address']//span[@itemprop='postalCode']//text()"
        XPATH_CATEGORIES = ".//div[@class='info']//div[contains(@class,'info-section')]//div[@class='categories']//text()"
        XPATH_WEBSITE = ".//div[@class='info']//div[contains(@class,'info-section')]//div[@class='links']//a[contains(@class,'website')]/@href"
        XPATH_RATING = ".//div[@class='info']//div[contains(@class,'info-section')]//div[contains(@class,'result-rating')]//span//text()"
        
        # Extract raw data
        raw_business_name = listing.xpath(XPATH_BUSINESS_NAME)
        raw_business_telephone = listing.xpath(XPATH_TELEPHONE)
        raw_business_page = listing.xpath(XPATH_BUSINESS_PAGE)
        raw_categories = listing.xpath(XPATH_CATEGORIES)
        raw_website = listing.xpath(XPATH_WEBSITE)
        raw_rating = listing.xpath(XPATH_RATING)
        raw_street = listing.xpath(XPATH_STREET)
        raw_locality = listing.xpath(XPATH_LOCALITY)
        raw_region = listing.xpath(XPATH_REGION)
        raw_zip_code = listing.xpath(XPATH_ZIP_CODE)
        
        # Clean and process data
        business_name = ''.join(raw_business_name).strip() if raw_business_name else None
        telephone = ''.join(raw_business_telephone).strip() if raw_business_telephone else None
        business_page = ''.join(raw_business_page).strip() if raw_business_page else None
        category = ', '.join(raw_categories).strip() if raw_categories else None
        website = ''.join(raw_website).strip() if raw_website else None
        rating = ''.join(raw_rating).replace("(", "").replace(")", "").strip() if raw_rating else None
        street = ''.join(raw_street).strip() if raw_street else None
        
        # Process locality (city, state, zip)
        locality_text = ''.join(raw_locality).replace(',\xa0', '').strip() if raw_locality else ''
        region = ''.join(raw_region).strip() if raw_region else None
        zipcode = ''.join(raw_zip_code).strip() if raw_zip_code else None
        
        # Try to parse city from locality
        city = None
        if locality_text:
            try:
                # Handle different locality formats
                if ',' in locality_text:
                    city = locality_text.split(',')[0].strip()
                else:
                    city = locality_text.strip()
            except:
                city = locality_text
        
        # Build full address
        address_parts = [street, city, region, zipcode]
        full_address = ', '.join([part for part in address_parts if part])
        
        business_info = {
            'business_name': business_name,
            'address': full_address if full_address else None,
            'street': street,
            'city': city,
            'state': region,
            'zipcode': zipcode,
            'phone': telephone,
            'website': website,
            'email': None,  # Yellow Pages doesn't typically provide email
            'category': category,
            'latitude': None,  # Not available from Yellow Pages
            'longitude': None,  # Not available from Yellow Pages
            'source': 'yellowpages',
            'source_id': business_page,
            'rating': rating,
            'business_page': business_page,
            'listing_url': source_url
        }
        
        return business_info
    
    def scrape_all_combinations(self) -> List[Dict[str, Any]]:
        """Scrape all keyword/location combinations"""
        logger.info("Starting Yellow Pages scraping...")
        
        all_businesses = []
        total_combinations = len(YELLOWPAGES_SEARCH_TERMS) * len(PITTSBURGH_AREA_CITIES)
        
        with tqdm(total=total_combinations, desc="Scraping Yellow Pages") as pbar:
            for keyword in YELLOWPAGES_SEARCH_TERMS:
                for city in PITTSBURGH_AREA_CITIES:
                    try:
                        # Scrape first page
                        businesses = self.parse_listing_page(keyword, city, page=1)
                        all_businesses.extend(businesses)
                        
                        # Try to scrape additional pages if first page had results
                        if businesses and len(businesses) >= 10:  # Typical page size
                            for page in range(2, 4):  # Scrape up to 3 pages per search
                                more_businesses = self.parse_listing_page(keyword, city, page)
                                if more_businesses:
                                    all_businesses.extend(more_businesses)
                                else:
                                    break  # No more results
                        
                        pbar.set_description(f"Found {len(all_businesses)} businesses")
                        pbar.update(1)
                        
                    except Exception as e:
                        logger.error(f"Error scraping {keyword} in {city}: {e}")
                        pbar.update(1)
                        continue
        
        logger.info(f"Yellow Pages scraping complete. Found {len(all_businesses)} businesses")
        return all_businesses
    
    def save_to_csv(self, businesses: List[Dict[str, Any]], filename: str):
        """Save businesses to CSV file"""
        if not businesses:
            logger.warning("No businesses to save")
            return
        
        df = pd.DataFrame(businesses)
        # Remove duplicates based on business name and phone
        df = df.drop_duplicates(subset=['business_name', 'phone'], keep='first')
        
        df.to_csv(filename, index=False)
        logger.info(f"Saved {len(df)} unique businesses to {filename}")

def main():
    """Main function to run Yellow Pages scraper"""
    scraper = YellowPagesScraper()
    
    # Scrape businesses
    businesses = scraper.scrape_all_combinations()
    
    # Save to CSV
    scraper.save_to_csv(businesses, YELLOWPAGES_OUTPUT)
    
    # Print summary
    if businesses:
        print(f"\n=== Yellow Pages Scraping Complete ===")
        print(f"Total businesses found: {len(businesses)}")
        print(f"Data saved to: {YELLOWPAGES_OUTPUT}")
        
        # Show sample data
        df = pd.DataFrame(businesses)
        print(f"\nSample data:")
        print(df[['business_name', 'category', 'city', 'phone', 'website']].head())
        
        # Show category breakdown
        if 'category' in df.columns:
            print(f"\nCategory breakdown:")
            print(df['category'].value_counts().head(10))
    else:
        print("No businesses found!")

if __name__ == "__main__":
    main()
