#!/usr/bin/env python3
"""
Overpass API scraper for collecting business data from OpenStreetMap
Focuses on businesses within 100 miles of Pittsburgh, PA
"""

import requests
import pandas as pd
import time
import logging
from typing import List, Dict, Any
from tqdm import tqdm
import json

from config import (
    PITTSBURGH_LAT, PITTSBURGH_LNG, SEARCH_RADIUS_METERS,
    OVERPASS_AMENITIES, OVERPASS_DELAY, OVERPASS_OUTPUT
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OverpassScraper:
    def __init__(self):
        self.api_url = "https://overpass-api.de/api/interpreter"
        self.businesses = []
        
    def build_query(self, amenity_types: List[str]) -> str:
        """Build Overpass QL query for business amenities"""
        amenity_filter = "|".join(amenity_types)
        
        query = f"""
        [out:json][timeout:60];
        (
          node["amenity"~"{amenity_filter}"](around:{SEARCH_RADIUS_METERS},{PITTSBURGH_LAT},{PITTSBURGH_LNG});
          way["amenity"~"{amenity_filter}"](around:{SEARCH_RADIUS_METERS},{PITTSBURGH_LAT},{PITTSBURGH_LNG});
          relation["amenity"~"{amenity_filter}"](around:{SEARCH_RADIUS_METERS},{PITTSBURGH_LAT},{PITTSBURGH_LNG});
          
          node["office"](around:{SEARCH_RADIUS_METERS},{PITTSBURGH_LAT},{PITTSBURGH_LNG});
          way["office"](around:{SEARCH_RADIUS_METERS},{PITTSBURGH_LAT},{PITTSBURGH_LNG});
          
          node["building"="office"](around:{SEARCH_RADIUS_METERS},{PITTSBURGH_LAT},{PITTSBURGH_LNG});
          way["building"="office"](around:{SEARCH_RADIUS_METERS},{PITTSBURGH_LAT},{PITTSBURGH_LNG});
          
          node["building"="commercial"](around:{SEARCH_RADIUS_METERS},{PITTSBURGH_LAT},{PITTSBURGH_LNG});
          way["building"="commercial"](around:{SEARCH_RADIUS_METERS},{PITTSBURGH_LAT},{PITTSBURGH_LNG});
        );
        out center meta;
        """
        return query
    
    def extract_business_info(self, element: Dict[str, Any]) -> Dict[str, Any]:
        """Extract business information from OSM element"""
        tags = element.get('tags', {})

        # Get coordinates
        if element.get('type') == 'node':
            lat = element.get('lat')
            lon = element.get('lon')
        else:
            # For ways and relations, use center coordinates if available
            if 'center' in element:
                center = element.get('center', {})
                lat = center.get('lat')
                lon = center.get('lon')
            else:
                lat = element.get('lat')
                lon = element.get('lon')
        
        # Extract business details
        business_name = (
            tags.get('name') or 
            tags.get('brand') or 
            tags.get('operator') or 
            'Unknown Business'
        )
        
        # Build address
        address_parts = []
        if tags.get('addr:housenumber'):
            address_parts.append(tags['addr:housenumber'])
        if tags.get('addr:street'):
            address_parts.append(tags['addr:street'])
        
        street = ' '.join(address_parts) if address_parts else None
        city = tags.get('addr:city')
        state = tags.get('addr:state')
        zipcode = tags.get('addr:postcode')
        
        # Full address
        full_address_parts = [street, city, state, zipcode]
        full_address = ', '.join([part for part in full_address_parts if part])
        
        # Business type/category
        amenity = tags.get('amenity')
        office_type = tags.get('office')
        building_type = tags.get('building')
        
        category = amenity or office_type or building_type or 'business'
        
        business_info = {
            'business_name': business_name,
            'address': full_address if full_address else None,
            'street': street,
            'city': city,
            'state': state,
            'zipcode': zipcode,
            'phone': tags.get('phone') or tags.get('contact:phone'),
            'website': tags.get('website') or tags.get('contact:website'),
            'email': tags.get('email') or tags.get('contact:email'),
            'category': category,
            'latitude': lat,
            'longitude': lon,
            'source': 'overpass',
            'source_id': f"osm_{element['type']}_{element['id']}",
            'amenity': amenity,
            'office_type': office_type,
            'building_type': building_type,
            'osm_id': element['id'],
            'osm_type': element['type']
        }
        
        return business_info
    
    def scrape_businesses(self) -> List[Dict[str, Any]]:
        """Scrape businesses using Overpass API"""
        logger.info("Starting Overpass API scraping...")

        try:
            # Build and execute query
            query = self.build_query(OVERPASS_AMENITIES)
            logger.info(f"Executing Overpass query...")

            # Make HTTP request to Overpass API
            response = requests.post(self.api_url, data=query, timeout=300)
            response.raise_for_status()

            result = response.json()
            elements = result.get('elements', [])
            logger.info(f"Found {len(elements)} OSM elements")

            # Process results
            businesses = []
            for element in tqdm(elements, desc="Processing OSM elements"):
                try:
                    business_info = self.extract_business_info(element)

                    # Filter out entries without names or with very generic names
                    if (business_info['business_name'] and
                        business_info['business_name'] != 'Unknown Business' and
                        len(business_info['business_name']) > 2):
                        businesses.append(business_info)

                    # Rate limiting
                    time.sleep(OVERPASS_DELAY)

                except Exception as e:
                    logger.warning(f"Error processing OSM element: {e}")
                    continue

            logger.info(f"Extracted {len(businesses)} valid businesses")
            return businesses

        except Exception as e:
            logger.error(f"Error in Overpass API scraping: {e}")
            return []
    
    def save_to_csv(self, businesses: List[Dict[str, Any]], filename: str):
        """Save businesses to CSV file"""
        if not businesses:
            logger.warning("No businesses to save")
            return
        
        df = pd.DataFrame(businesses)
        df.to_csv(filename, index=False)
        logger.info(f"Saved {len(businesses)} businesses to {filename}")

def main():
    """Main function to run Overpass scraper"""
    scraper = OverpassScraper()
    
    # Scrape businesses
    businesses = scraper.scrape_businesses()
    
    # Save to CSV
    scraper.save_to_csv(businesses, OVERPASS_OUTPUT)
    
    # Print summary
    if businesses:
        print(f"\n=== Overpass API Scraping Complete ===")
        print(f"Total businesses found: {len(businesses)}")
        print(f"Data saved to: {OVERPASS_OUTPUT}")
        
        # Show sample data
        df = pd.DataFrame(businesses)
        print(f"\nSample data:")
        print(df[['business_name', 'category', 'city', 'phone', 'website']].head())
        
        # Show category breakdown
        print(f"\nCategory breakdown:")
        print(df['category'].value_counts().head(10))
    else:
        print("No businesses found!")

if __name__ == "__main__":
    main()
